"""
Gestor M3U con TMDB
==================

Panel principal para:
1. Subir y desglosar listas M3U
2. Detectar títulos faltantes en XUI One
3. <PERSON><PERSON> con metadatos TMDB
4. Exportar con nombres correctos
"""

import asyncio
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
import logging
from typing import Dict, List, Optional
import threading
import json
import re
import os
from urllib.parse import unquote

class M3UTMDBManager:
    """Gestor principal M3U con TMDB"""

    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.m3u_tmdb")

        # Estado
        self.m3u_entries = []
        self.missing_content = []
        self.tmdb_enriched = []
        self.current_file = None

        # Crear interfaz
        self._create_layout()
        self._create_upload_section()
        self._create_analysis_section()
        self._create_results_section()
        self._create_export_section()

    def _create_layout(self):
        """Crear layout principal"""
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🎬 Gestor M3U con TMDB",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(20, 10))

        # Descripción
        desc_label = ctk.CTkLabel(
            self.main_frame,
            text="Sube M3U → Detecta faltantes → Enriquece con TMDB → Exporta correctamente",
            font=("Arial", 12)
        )
        desc_label.pack(pady=(0, 20))

    def _create_upload_section(self):
        """Crear sección de subida de M3U"""
        upload_frame = ctk.CTkFrame(self.main_frame)
        upload_frame.pack(fill="x", padx=20, pady=10)

        # Título sección
        upload_title = ctk.CTkLabel(upload_frame, text="📁 1. SUBIR ARCHIVO M3U", font=("Arial", 16, "bold"))
        upload_title.pack(pady=(15, 10))

        # Botón subir
        upload_btn = ctk.CTkButton(
            upload_frame,
            text="📂 Seleccionar archivo M3U",
            command=self._upload_m3u_file,
            height=40,
            font=("Arial", 14)
        )
        upload_btn.pack(pady=10)

        # Info archivo
        self.file_info_label = ctk.CTkLabel(upload_frame, text="Ningún archivo seleccionado")
        self.file_info_label.pack(pady=(0, 15))

    def _create_analysis_section(self):
        """Crear sección de análisis"""
        analysis_frame = ctk.CTkFrame(self.main_frame)
        analysis_frame.pack(fill="x", padx=20, pady=10)

        # Título sección
        analysis_title = ctk.CTkLabel(analysis_frame, text="🔍 2. ANÁLISIS Y DETECCIÓN", font=("Arial", 16, "bold"))
        analysis_title.pack(pady=(15, 10))

        # Botones de análisis
        buttons_frame = ctk.CTkFrame(analysis_frame)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        self.analyze_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 Analizar M3U",
            command=self._analyze_m3u,
            state="disabled"
        )
        self.analyze_btn.pack(side="left", padx=5)

        self.detect_missing_btn = ctk.CTkButton(
            buttons_frame,
            text="🎯 Detectar Faltantes",
            command=self._detect_missing_content,
            state="disabled"
        )
        self.detect_missing_btn.pack(side="left", padx=5)

        self.enrich_tmdb_btn = ctk.CTkButton(
            buttons_frame,
            text="🎬 Enriquecer TMDB",
            command=self._enrich_with_tmdb,
            state="disabled"
        )
        self.enrich_tmdb_btn.pack(side="left", padx=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ctk.CTkProgressBar(analysis_frame, variable=self.progress_var)
        self.progress_bar.pack(fill="x", padx=20, pady=10)
        self.progress_bar.set(0)

        # Status
        self.status_label = ctk.CTkLabel(analysis_frame, text="Listo para comenzar")
        self.status_label.pack(pady=(0, 15))

    def _create_results_section(self):
        """Crear sección de resultados"""
        results_frame = ctk.CTkFrame(self.main_frame)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Título sección
        results_title = ctk.CTkLabel(results_frame, text="📊 3. RESULTADOS", font=("Arial", 16, "bold"))
        results_title.pack(pady=(15, 10))

        # Notebook para pestañas
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Pestaña M3U Original
        self.m3u_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.m3u_frame, text="M3U Original")

        # Pestaña Faltantes
        self.missing_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.missing_frame, text="Contenido Faltante")

        # Pestaña TMDB Enriquecido
        self.tmdb_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.tmdb_frame, text="TMDB Enriquecido")

        # Crear treeviews
        self._create_treeviews()

    def _create_treeviews(self):
        """Crear treeviews para cada pestaña"""
        # TreeView M3U Original
        m3u_columns = ("Título", "Grupo", "URL")
        self.m3u_tree = ttk.Treeview(self.m3u_frame, columns=m3u_columns, show="headings", height=10)

        for col in m3u_columns:
            self.m3u_tree.heading(col, text=col)
            self.m3u_tree.column(col, width=200)

        m3u_scroll = ttk.Scrollbar(self.m3u_frame, orient="vertical", command=self.m3u_tree.yview)
        self.m3u_tree.configure(yscrollcommand=m3u_scroll.set)

        self.m3u_tree.pack(side="left", fill="both", expand=True)
        m3u_scroll.pack(side="right", fill="y")

        # TreeView Faltantes
        missing_columns = ("Título", "Tipo", "En XUI", "TMDB ID", "Estado")
        self.missing_tree = ttk.Treeview(self.missing_frame, columns=missing_columns, show="headings", height=10)

        for col in missing_columns:
            self.missing_tree.heading(col, text=col)
            self.missing_tree.column(col, width=150)

        missing_scroll = ttk.Scrollbar(self.missing_frame, orient="vertical", command=self.missing_tree.yview)
        self.missing_tree.configure(yscrollcommand=missing_scroll.set)

        self.missing_tree.pack(side="left", fill="both", expand=True)
        missing_scroll.pack(side="right", fill="y")

        # TreeView TMDB Enriquecido
        tmdb_columns = ("Título Original", "Título TMDB", "Año", "Rating", "Género", "Nuevo Nombre")
        self.tmdb_tree = ttk.Treeview(self.tmdb_frame, columns=tmdb_columns, show="headings", height=10)

        for col in tmdb_columns:
            self.tmdb_tree.heading(col, text=col)
            self.tmdb_tree.column(col, width=150)

        tmdb_scroll = ttk.Scrollbar(self.tmdb_frame, orient="vertical", command=self.tmdb_tree.yview)
        self.tmdb_tree.configure(yscrollcommand=tmdb_scroll.set)

        self.tmdb_tree.pack(side="left", fill="both", expand=True)
        tmdb_scroll.pack(side="right", fill="y")

    def _create_export_section(self):
        """Crear sección de exportación"""
        export_frame = ctk.CTkFrame(self.main_frame)
        export_frame.pack(fill="x", padx=20, pady=10)

        # Título sección
        export_title = ctk.CTkLabel(export_frame, text="📤 4. EXPORTAR", font=("Arial", 16, "bold"))
        export_title.pack(pady=(15, 10))

        # Botones de exportación
        export_buttons_frame = ctk.CTkFrame(export_frame)
        export_buttons_frame.pack(fill="x", padx=20, pady=10)

        self.export_missing_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📋 Exportar Lista Faltantes",
            command=self._export_missing_list,
            state="disabled"
        )
        self.export_missing_btn.pack(side="left", padx=5)

        self.export_m3u_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📺 Exportar M3U Corregido",
            command=self._export_corrected_m3u,
            state="disabled"
        )
        self.export_m3u_btn.pack(side="left", padx=5)

        self.export_json_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📄 Exportar JSON Metadatos",
            command=self._export_metadata_json,
            state="disabled"
        )
        self.export_json_btn.pack(side="left", padx=5)

        # Info exportación
        self.export_info_label = ctk.CTkLabel(export_frame, text="")
        self.export_info_label.pack(pady=(0, 15))

    def _upload_m3u_file(self):
        """Subir archivo M3U"""
        try:
            file_path = filedialog.askopenfilename(
                title="Seleccionar archivo M3U",
                filetypes=[("M3U files", "*.m3u"), ("M3U8 files", "*.m3u8"), ("All files", "*.*")]
            )

            if file_path:
                self.current_file = file_path
                filename = os.path.basename(file_path)

                # Leer archivo para obtener info básica
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.split('\n')
                    entry_count = len([line for line in lines if line.startswith('#EXTINF')])

                self.file_info_label.configure(text=f"📁 {filename} - {entry_count:,} entradas")
                self.analyze_btn.configure(state="normal")
                self.status_label.configure(text="Archivo cargado. Listo para analizar.")

        except Exception as e:
            self.logger.error(f"Error al cargar archivo M3U: {str(e)}")
            messagebox.showerror("Error", f"Error al cargar archivo: {str(e)}")

    def _analyze_m3u(self):
        """Analizar archivo M3U"""
        if not self.current_file:
            messagebox.showwarning("Advertencia", "Primero selecciona un archivo M3U")
            return

        def analyze_thread():
            try:
                self.status_label.configure(text="Analizando archivo M3U...")
                self.progress_bar.set(0.1)

                # Parsear M3U
                self.m3u_entries = self._parse_m3u_file(self.current_file)
                self.progress_bar.set(0.5)

                # Actualizar TreeView
                self._update_m3u_treeview()
                self.progress_bar.set(1.0)

                # Habilitar siguiente paso
                self.detect_missing_btn.configure(state="normal")
                self.status_label.configure(text=f"M3U analizado: {len(self.m3u_entries):,} entradas procesadas")

            except Exception as e:
                self.logger.error(f"Error al analizar M3U: {str(e)}")
                self.status_label.configure(text=f"Error: {str(e)}")

        threading.Thread(target=analyze_thread, daemon=True).start()

    def _parse_m3u_file(self, file_path: str) -> List[Dict]:
        """Parsear archivo M3U"""
        entries = []

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            lines = content.split('\n')
            current_entry = {}

            for line in lines:
                line = line.strip()

                if line.startswith('#EXTINF'):
                    # Parsear línea EXTINF
                    current_entry = self._parse_extinf_line(line)

                elif line and not line.startswith('#') and current_entry:
                    # URL del stream
                    current_entry['url'] = line
                    current_entry['type'] = self._detect_content_type(current_entry.get('title', ''))
                    entries.append(current_entry.copy())
                    current_entry = {}

            return entries

        except Exception as e:
            self.logger.error(f"Error al parsear M3U: {str(e)}")
            return []

    def _parse_extinf_line(self, line: str) -> Dict:
        """Parsear línea EXTINF"""
        entry = {}

        try:
            # Extraer título (después de la última coma)
            if ',' in line:
                title = line.split(',')[-1].strip()
                entry['title'] = title

            # Extraer atributos
            # tvg-id
            if 'tvg-id=' in line:
                match = re.search(r'tvg-id="([^"]*)"', line)
                if match:
                    entry['tvg_id'] = match.group(1)

            # tvg-name
            if 'tvg-name=' in line:
                match = re.search(r'tvg-name="([^"]*)"', line)
                if match:
                    entry['tvg_name'] = match.group(1)

            # tvg-logo
            if 'tvg-logo=' in line:
                match = re.search(r'tvg-logo="([^"]*)"', line)
                if match:
                    entry['logo'] = match.group(1)

            # group-title
            if 'group-title=' in line:
                match = re.search(r'group-title="([^"]*)"', line)
                if match:
                    entry['group'] = match.group(1)

            return entry

        except Exception as e:
            self.logger.error(f"Error al parsear línea EXTINF: {str(e)}")
            return {'title': 'Error parsing'}

    def _detect_content_type(self, title: str) -> str:
        """Detectar tipo de contenido basado en el título"""
        title_lower = title.lower()

        # Detectar películas
        movie_indicators = ['1080p', '720p', '4k', 'hd', 'movie', 'film', 'pelicula', '2024', '2023', '2022']
        if any(indicator in title_lower for indicator in movie_indicators):
            return 'movie'

        # Detectar series
        series_indicators = ['s01', 's02', 's03', 'season', 'temporada', 'episode', 'ep', 'serie']
        if any(indicator in title_lower for indicator in series_indicators):
            return 'series'

        # Por defecto, live TV
        return 'live'

    def _update_m3u_treeview(self):
        """Actualizar TreeView de M3U"""
        # Limpiar
        for item in self.m3u_tree.get_children():
            self.m3u_tree.delete(item)

        # Agregar entradas
        for entry in self.m3u_entries:
            self.m3u_tree.insert("", "end", values=(
                entry.get('title', 'Sin título')[:50],
                entry.get('group', 'Sin grupo')[:30],
                entry.get('url', 'Sin URL')[:50]
            ))

    def _detect_missing_content(self):
        """Detectar contenido faltante en XUI One"""
        if not self.m3u_entries:
            messagebox.showwarning("Advertencia", "Primero analiza el archivo M3U")
            return

        def detect_thread():
            try:
                self.status_label.configure(text="Detectando contenido faltante...")
                self.progress_bar.set(0.1)

                # Obtener contenido actual de XUI One
                asyncio.run(self._compare_with_xui_content())

            except Exception as e:
                self.logger.error(f"Error al detectar faltantes: {str(e)}")
                self.status_label.configure(text=f"Error: {str(e)}")

        threading.Thread(target=detect_thread, daemon=True).start()

    async def _compare_with_xui_content(self):
        """Comparar M3U con contenido XUI One"""
        try:
            # Obtener películas de XUI One
            xui_movies = await self.db_manager.get_movies(limit=50000)  # Obtener todas
            xui_titles = {movie.get('stream_display_name', '').lower() for movie in xui_movies}

            self.progress_bar.set(0.3)

            # Filtrar solo películas del M3U
            m3u_movies = [entry for entry in self.m3u_entries if entry.get('type') == 'movie']

            self.progress_bar.set(0.5)

            # Detectar faltantes
            self.missing_content = []
            for entry in m3u_movies:
                title = entry.get('title', '').lower()
                clean_title = self._clean_title_for_comparison(title)

                # Buscar coincidencias
                found = any(clean_title in xui_title or xui_title in clean_title
                           for xui_title in xui_titles)

                if not found:
                    entry['in_xui'] = False
                    entry['status'] = 'Faltante'
                    self.missing_content.append(entry)
                else:
                    entry['in_xui'] = True
                    entry['status'] = 'Existe'

            self.progress_bar.set(0.8)

            # Actualizar TreeView
            self._update_missing_treeview()

            self.progress_bar.set(1.0)

            # Habilitar siguiente paso
            self.enrich_tmdb_btn.configure(state="normal")
            self.status_label.configure(text=f"Detección completada: {len(self.missing_content):,} títulos faltantes")

        except Exception as e:
            self.logger.error(f"Error en comparación: {str(e)}")
            self.status_label.configure(text=f"Error: {str(e)}")

    def _clean_title_for_comparison(self, title: str) -> str:
        """Limpiar título para comparación"""
        # Remover calidades, años, etc.
        clean = re.sub(r'\b(1080p|720p|4k|hd|cam|ts|dvdrip|brrip)\b', '', title, flags=re.IGNORECASE)
        clean = re.sub(r'\b(20\d{2})\b', '', clean)  # Años
        clean = re.sub(r'[^\w\s]', ' ', clean)  # Caracteres especiales
        clean = ' '.join(clean.split())  # Espacios múltiples
        return clean.strip().lower()

    def _update_missing_treeview(self):
        """Actualizar TreeView de contenido faltante"""
        # Limpiar
        for item in self.missing_tree.get_children():
            self.missing_tree.delete(item)

        # Agregar faltantes
        for entry in self.missing_content:
            self.missing_tree.insert("", "end", values=(
                entry.get('title', 'Sin título')[:40],
                entry.get('type', 'Unknown'),
                "❌ No" if not entry.get('in_xui', False) else "✅ Sí",
                entry.get('tmdb_id', 'Pendiente'),
                entry.get('status', 'Pendiente')
            ))

    def _enrich_with_tmdb(self):
        """Enriquecer contenido faltante con metadatos TMDB"""
        if not self.missing_content:
            messagebox.showwarning("Advertencia", "Primero detecta el contenido faltante")
            return

        def enrich_thread():
            try:
                self.status_label.configure(text="Enriqueciendo con metadatos TMDB...")
                self.progress_bar.set(0.1)

                # Simular enriquecimiento TMDB (aquí iría la integración real con TMDB API)
                self.tmdb_enriched = []
                total = len(self.missing_content)

                for i, entry in enumerate(self.missing_content):
                    # Simular búsqueda TMDB
                    enriched = self._simulate_tmdb_enrichment(entry)
                    self.tmdb_enriched.append(enriched)

                    # Actualizar progreso
                    progress = 0.1 + (0.8 * (i + 1) / total)
                    self.progress_bar.set(progress)

                # Actualizar TreeView
                self._update_tmdb_treeview()

                self.progress_bar.set(1.0)

                # Habilitar exportación
                self.export_missing_btn.configure(state="normal")
                self.export_m3u_btn.configure(state="normal")
                self.export_json_btn.configure(state="normal")

                self.status_label.configure(text=f"Enriquecimiento completado: {len(self.tmdb_enriched):,} títulos procesados")

            except Exception as e:
                self.logger.error(f"Error en enriquecimiento TMDB: {str(e)}")
                self.status_label.configure(text=f"Error: {str(e)}")

        threading.Thread(target=enrich_thread, daemon=True).start()

    def _simulate_tmdb_enrichment(self, entry: Dict) -> Dict:
        """Simular enriquecimiento TMDB (aquí iría la integración real)"""
        # Esta función simula la búsqueda en TMDB
        # En la implementación real, aquí se haría la llamada a la API de TMDB

        title = entry.get('title', '')
        clean_title = self._clean_title_for_tmdb_search(title)

        # Simular datos TMDB
        enriched = entry.copy()
        enriched.update({
            'tmdb_id': f"sim_{hash(clean_title) % 100000}",
            'tmdb_title': clean_title.title(),
            'year': self._extract_year_from_title(title) or '2024',
            'rating': round(5.0 + (hash(clean_title) % 50) / 10, 1),
            'genre': 'Action, Drama',  # Simulado
            'overview': f"Descripción simulada para {clean_title}",
            'poster_url': f"https://image.tmdb.org/t/p/w500/poster_{hash(clean_title) % 1000}.jpg",
            'corrected_name': self._generate_corrected_name(clean_title, self._extract_year_from_title(title))
        })

        return enriched

    def _clean_title_for_tmdb_search(self, title: str) -> str:
        """Limpiar título para búsqueda TMDB"""
        # Remover calidades, formatos, etc.
        clean = re.sub(r'\b(1080p|720p|4k|uhd|hd|cam|ts|dvdrip|brrip|webrip|bluray)\b', '', title, flags=re.IGNORECASE)
        clean = re.sub(r'\([^)]*\)', '', clean)  # Contenido entre paréntesis
        clean = re.sub(r'\[[^\]]*\]', '', clean)  # Contenido entre corchetes
        clean = re.sub(r'[^\w\s]', ' ', clean)  # Caracteres especiales
        clean = ' '.join(clean.split())  # Espacios múltiples
        return clean.strip()

    def _extract_year_from_title(self, title: str) -> Optional[str]:
        """Extraer año del título"""
        match = re.search(r'\b(20\d{2})\b', title)
        return match.group(1) if match else None

    def _generate_corrected_name(self, title: str, year: Optional[str]) -> str:
        """Generar nombre corregido según estándares TMDB"""
        corrected = title.strip()

        # Agregar año si existe
        if year:
            corrected = f"{corrected} ({year})"

        # Formato estándar
        corrected = corrected.replace('  ', ' ')

        return corrected

    def _update_tmdb_treeview(self):
        """Actualizar TreeView de contenido enriquecido TMDB"""
        # Limpiar
        for item in self.tmdb_tree.get_children():
            self.tmdb_tree.delete(item)

        # Agregar enriquecidos
        for entry in self.tmdb_enriched:
            self.tmdb_tree.insert("", "end", values=(
                entry.get('title', 'Sin título')[:30],
                entry.get('tmdb_title', 'Sin título TMDB')[:30],
                entry.get('year', 'N/A'),
                entry.get('rating', 'N/A'),
                entry.get('genre', 'N/A')[:20],
                entry.get('corrected_name', 'Sin nombre')[:40]
            ))

    def _export_missing_list(self):
        """Exportar lista de contenido faltante"""
        if not self.tmdb_enriched:
            messagebox.showwarning("Advertencia", "Primero enriquece con TMDB")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="Guardar lista de faltantes",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("LISTA DE CONTENIDO FALTANTE EN XUI ONE\n")
                    f.write("=" * 50 + "\n\n")

                    for i, entry in enumerate(self.tmdb_enriched, 1):
                        f.write(f"{i:3d}. {entry.get('corrected_name', 'Sin nombre')}\n")
                        f.write(f"     Título original: {entry.get('title', 'N/A')}\n")
                        f.write(f"     TMDB ID: {entry.get('tmdb_id', 'N/A')}\n")
                        f.write(f"     Año: {entry.get('year', 'N/A')}\n")
                        f.write(f"     Rating: {entry.get('rating', 'N/A')}\n")
                        f.write(f"     URL: {entry.get('url', 'N/A')}\n")
                        f.write("\n")

                self.export_info_label.configure(text=f"✅ Lista exportada: {os.path.basename(file_path)}")

        except Exception as e:
            self.logger.error(f"Error al exportar lista: {str(e)}")
            messagebox.showerror("Error", f"Error al exportar: {str(e)}")

    def _export_corrected_m3u(self):
        """Exportar M3U con nombres corregidos"""
        if not self.tmdb_enriched:
            messagebox.showwarning("Advertencia", "Primero enriquece con TMDB")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="Guardar M3U corregido",
                defaultextension=".m3u",
                filetypes=[("M3U files", "*.m3u"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("#EXTM3U\n")

                    for entry in self.tmdb_enriched:
                        # Línea EXTINF con nombre corregido
                        extinf = f"#EXTINF:-1"

                        if entry.get('tmdb_id'):
                            extinf += f' tvg-id="{entry["tmdb_id"]}"'

                        if entry.get('logo'):
                            extinf += f' tvg-logo="{entry["logo"]}"'

                        if entry.get('group'):
                            extinf += f' group-title="{entry["group"]}"'

                        extinf += f',{entry.get("corrected_name", "Sin nombre")}\n'

                        f.write(extinf)
                        f.write(f"{entry.get('url', '')}\n")

                self.export_info_label.configure(text=f"✅ M3U exportado: {os.path.basename(file_path)}")

        except Exception as e:
            self.logger.error(f"Error al exportar M3U: {str(e)}")
            messagebox.showerror("Error", f"Error al exportar: {str(e)}")

    def _export_metadata_json(self):
        """Exportar metadatos en formato JSON"""
        if not self.tmdb_enriched:
            messagebox.showwarning("Advertencia", "Primero enriquece con TMDB")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="Guardar metadatos JSON",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                export_data = {
                    'metadata': {
                        'total_entries': len(self.tmdb_enriched),
                        'export_date': str(asyncio.get_event_loop().time()),
                        'source_file': os.path.basename(self.current_file) if self.current_file else 'Unknown'
                    },
                    'missing_content': self.tmdb_enriched
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                self.export_info_label.configure(text=f"✅ JSON exportado: {os.path.basename(file_path)}")

        except Exception as e:
            self.logger.error(f"Error al exportar JSON: {str(e)}")
            messagebox.showerror("Error", f"Error al exportar: {str(e)}")

    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()