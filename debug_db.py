"""
Script para debugging del DatabaseManager
"""

import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("1. Importando módulos...")

try:
    from config.settings import Settings
    print("✓ Settings importado")
except Exception as e:
    print(f"✗ Error importando Settings: {e}")
    exit(1)

try:
    from core.database_manager import DatabaseManager
    print("✓ DatabaseManager importado")
except Exception as e:
    print(f"✗ Error importando DatabaseManager: {e}")
    exit(1)

print("2. Inicializando Settings...")
try:
    settings = Settings()
    print("✓ Settings inicializado")
except Exception as e:
    print(f"✗ Error inicializando Settings: {e}")
    exit(1)

print("3. Creando DatabaseManager...")
try:
    db_manager = DatabaseManager(settings)
    print("✓ DatabaseManager creado")
except Exception as e:
    print(f"✗ Error creando DatabaseManager: {e}")
    exit(1)

print("4. Intentando inicializar pool...")
try:
    import asyncio
    
    async def test_init():
        await db_manager.initialize()
        print("✓ Pool inicializado")
        
    asyncio.run(test_init())
except Exception as e:
    print(f"✗ Error inicializando pool: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("✓ Todas las pruebas pasaron correctamente")
