"""
Test de Funcionalidades Avanzadas
=================================

Script para probar las nuevas funcionalidades de gestión de duplicados,
M3U, calidades y metadatos TMDB.
"""

import asyncio
import sys
import os
import json

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_advanced_features():
    """Probar todas las funcionalidades avanzadas"""
    print("🚀 Test de Funcionalidades Avanzadas")
    print("=" * 60)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa")
        
        # ========================================
        # ANÁLISIS DE DUPLICADOS AVANZADO
        # ========================================
        print("\n📊 ANÁLISIS DE DUPLICADOS AVANZADO")
        print("-" * 40)
        
        # Duplicados detallados
        print("2. Obteniendo duplicados detallados...")
        detailed_duplicates = await db_manager.get_detailed_duplicates()
        print(f"🔄 Duplicados detallados encontrados: {len(detailed_duplicates)}")
        
        for i, dup in enumerate(detailed_duplicates[:3]):
            print(f"   {i+1}. '{dup.get('stream_display_name', 'Sin nombre')}'")
            print(f"      - Aparece {dup.get('count', 0)} veces")
            print(f"      - IDs: {dup.get('ids', '')}")
            print(f"      - TMDB IDs: {dup.get('tmdb_ids', '')}")
            print(f"      - Tipos: {dup.get('types', '')}")
        
        # Duplicados por calidad
        print("\n3. Analizando duplicados por calidad...")
        quality_duplicates = await db_manager.get_duplicate_movies_by_quality()
        print(f"🎬 Películas duplicadas por calidad: {len(quality_duplicates)}")
        
        # Agrupar por nombre para mostrar mejor
        grouped_by_name = {}
        for movie in quality_duplicates[:15]:  # Limitar para el ejemplo
            name = movie.get('stream_display_name', 'Sin nombre')
            if name not in grouped_by_name:
                grouped_by_name[name] = []
            grouped_by_name[name].append(movie)
        
        for i, (name, movies) in enumerate(list(grouped_by_name.items())[:3]):
            print(f"   {i+1}. '{name}' - {len(movies)} versiones:")
            for movie in movies:
                print(f"      - {movie.get('quality', 'Unknown')} (ID: {movie.get('id')}, Rating: {movie.get('rating', 'N/A')})")
        
        # ========================================
        # ANÁLISIS DE CALIDAD
        # ========================================
        print("\n🎯 ANÁLISIS DE CALIDAD")
        print("-" * 40)
        
        print("4. Analizando distribución de calidades...")
        quality_dist = await db_manager.analyze_quality_distribution()
        print("📈 Distribución de calidades:")
        for quality, count in quality_dist.items():
            percentage = (count / sum(quality_dist.values())) * 100 if quality_dist else 0
            print(f"   - {quality}: {count} ({percentage:.1f}%)")
        
        print("\n5. Identificando contenido de baja calidad...")
        low_quality = await db_manager.get_low_quality_movies(limit=10)
        print(f"📉 Películas de baja calidad encontradas: {len(low_quality)}")
        
        for i, movie in enumerate(low_quality[:5]):
            print(f"   {i+1}. {movie.get('stream_display_name', 'Sin nombre')} (ID: {movie.get('id')})")
        
        # ========================================
        # GESTIÓN DE CATEGORÍAS
        # ========================================
        print("\n📁 GESTIÓN DE CATEGORÍAS")
        print("-" * 40)
        
        print("6. Obteniendo categorías existentes...")
        categories = await db_manager.get_categories()
        print(f"📂 Categorías totales: {len(categories)}")
        
        # Mostrar algunas categorías por tipo
        movie_cats = [c for c in categories if c.get('category_type') == 'movie'][:5]
        live_cats = [c for c in categories if c.get('category_type') == 'live'][:5]
        
        print("   Categorías de películas:")
        for cat in movie_cats:
            print(f"   - {cat.get('category_name', 'Sin nombre')} (ID: {cat.get('id')})")
        
        print("   Categorías de TV en vivo:")
        for cat in live_cats:
            print(f"   - {cat.get('category_name', 'Sin nombre')} (ID: {cat.get('id')})")
        
        # ========================================
        # REPORTE DE SALUD DEL CONTENIDO
        # ========================================
        print("\n🏥 REPORTE DE SALUD DEL CONTENIDO")
        print("-" * 40)
        
        print("7. Generando reporte de salud...")
        health_report = await db_manager.get_content_health_report()
        
        if health_report:
            print("📋 Reporte de salud del contenido:")
            
            basic_stats = health_report.get('basic_stats', {})
            print(f"   - Total streams: {basic_stats.get('total_streams', 0)}")
            print(f"   - Películas: {basic_stats.get('total_movies', 0)}")
            print(f"   - Series: {basic_stats.get('total_series', 0)}")
            print(f"   - TV en vivo: {basic_stats.get('total_live', 0)}")
            
            print(f"   - Duplicados detectados: {health_report.get('duplicates_count', 0)}")
            print(f"   - Sin información TMDB: {health_report.get('no_tmdb_count', 0)}")
            print(f"   - Streams rotos: {health_report.get('broken_streams_count', 0)}")
            
            health_score = health_report.get('health_score', 0)
            print(f"   - 🎯 Puntuación de salud: {health_score:.1f}/100")
            
            if health_score >= 80:
                print("   ✅ Estado: EXCELENTE")
            elif health_score >= 60:
                print("   ⚠️ Estado: BUENO")
            elif health_score >= 40:
                print("   ⚠️ Estado: REGULAR")
            else:
                print("   ❌ Estado: NECESITA ATENCIÓN")
        
        # ========================================
        # SIMULACIÓN DE GESTIÓN M3U
        # ========================================
        print("\n📺 SIMULACIÓN DE GESTIÓN M3U")
        print("-" * 40)
        
        print("8. Simulando entrada M3U...")
        # Simular entrada M3U
        sample_m3u_entry = {
            'title': 'Avatar: El Camino del Agua (2022) 4K',
            'url': 'http://example.com/avatar2.mkv',
            'logo': 'http://example.com/avatar2.jpg',
            'group_title': 'Películas 4K',
            'category_id': '[1927]'  # Categoría de estrenos
        }
        
        # Simular datos TMDB
        sample_tmdb_data = {
            'id': 76600,
            'title': 'Avatar: The Way of Water',
            'original_title': 'Avatar: The Way of Water',
            'overview': 'Set more than a decade after the events of the first film...',
            'release_date': '2022-12-14',
            'vote_average': 7.6,
            'poster_path': '/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg',
            'backdrop_path': '/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
            'genres': [{'id': 878, 'name': 'Science Fiction'}, {'id': 12, 'name': 'Adventure'}],
            'runtime': 192
        }
        
        print(f"   📝 Entrada M3U: {sample_m3u_entry['title']}")
        print(f"   🎬 Datos TMDB: {sample_tmdb_data['title']} (ID: {sample_tmdb_data['id']})")
        print("   ℹ️ Nota: Esta es una simulación - no se insertará en la base de datos")
        
        # Mostrar cómo se procesaría
        stream_type = db_manager._determine_stream_type(sample_m3u_entry)
        type_names = {1: "Live TV", 2: "Movie", 3: "Series"}
        print(f"   🔍 Tipo detectado: {type_names.get(stream_type, 'Unknown')}")
        
        movie_properties = db_manager._create_movie_properties_from_tmdb(sample_tmdb_data)
        properties_preview = json.loads(movie_properties)
        print(f"   📊 Metadatos generados: {len(properties_preview)} campos")
        print(f"      - Título: {properties_preview.get('name', 'N/A')}")
        print(f"      - Año: {properties_preview.get('year', 'N/A')}")
        print(f"      - Rating: {properties_preview.get('rating', 'N/A')}")
        print(f"      - Género: {properties_preview.get('genre', 'N/A')}")
        
        print("\n✅ Test de funcionalidades avanzadas completado exitosamente")
        print("\n🎉 RESUMEN DE CAPACIDADES IMPLEMENTADAS:")
        print("   ✅ Detección y análisis detallado de duplicados")
        print("   ✅ Gestión de duplicados por calidad")
        print("   ✅ Análisis de distribución de calidades")
        print("   ✅ Identificación de contenido de baja calidad")
        print("   ✅ Gestión avanzada de categorías")
        print("   ✅ Reporte de salud del contenido")
        print("   ✅ Procesamiento de entradas M3U con metadatos TMDB")
        print("   ✅ Actualización masiva de streams con datos TMDB")
        print("   ✅ Herramientas para renombrar, mover y fusionar contenido")
        
    except Exception as e:
        print(f"\n❌ Error durante el test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_advanced_features())
    if success:
        print("\n🚀 El sistema está listo para gestión avanzada de contenido IPTV")
        print("   - Gestión inteligente de duplicados")
        print("   - Análisis de calidad y salud del contenido")
        print("   - Integración M3U con metadatos TMDB")
        print("   - Herramientas de organización y limpieza")
    else:
        print("\n💥 Test falló")
        print("Revisa la configuración y conexión a la base de datos")
        sys.exit(1)
