"""
Gestor de base de datos
======================

Maneja todas las operaciones de base de datos usando MySQL/MariaDB
con soporte para operaciones asíncronas y pool de conexiones.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple

import mysql.connector
from mysql.connector import pooling
from contextlib import asynccontextmanager

from config.database import QUERIES, CONNECTION_CONFIG

class DatabaseManager:
    """Gestor de base de datos con soporte asíncrono"""
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("iptv_manager.database")
        self.pool = None
        self._initialized = False
        
    async def initialize(self):
        """Inicializar el pool de conexiones"""
        if self._initialized:
            return
            
        try:
            config = self.settings.get_db_config()
            config.update(CONNECTION_CONFIG)
            
            # Crear pool de conexiones
            self.pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name=config["pool_name"],
                pool_size=config["pool_size"],
                pool_reset_session=config["pool_reset_session"],
                **{k: v for k, v in config.items() if k not in ["pool_name", "pool_size", "pool_reset_session"]}
            )
            
            self._initialized = True
            self.logger.info("Pool de conexiones inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error al inicializar pool de conexiones: {str(e)}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """Obtener conexión del pool"""
        if not self._initialized:
            await self.initialize()
            
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
        except Exception as e:
            self.logger.error(f"Error en conexión de base de datos: {str(e)}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    async def test_connection(self) -> bool:
        """Probar conexión a la base de datos"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
        except Exception as e:
            self.logger.error(f"Error al probar conexión: {str(e)}")
            return False

    async def verify_tables(self) -> bool:
        """Verificar que existan las tablas necesarias de XUI One"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()

                # Tablas requeridas de XUI One
                required_tables = [
                    "streams", "streams_categories", "streams_series",
                    "streams_episodes", "streams_types", "streams_servers"
                ]

                # Verificar cada tabla
                for table_name in required_tables:
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    result = cursor.fetchone()
                    if not result:
                        self.logger.error(f"Tabla requerida '{table_name}' no encontrada")
                        cursor.close()
                        return False

                cursor.close()
                self.logger.info("Todas las tablas requeridas de XUI One están presentes")
                return True

        except Exception as e:
            self.logger.error(f"Error al verificar tablas: {str(e)}")
            return False
    
    async def create_tables(self):
        """
        NOTA: Este método no crea tablas ya que trabajamos con una base de datos XUI One existente.
        Solo verifica que las tablas necesarias estén presentes.
        """
        try:
            if not await self.verify_tables():
                raise Exception("Las tablas requeridas de XUI One no están presentes en la base de datos")

            self.logger.info("Verificación de tablas XUI One completada correctamente")

        except Exception as e:
            self.logger.error(f"Error al verificar tablas XUI One: {str(e)}")
            raise
    

    

    

    
    async def execute_query(self, query: str, params: tuple | None = None) -> List[Dict]:
        """Ejecutar consulta SELECT"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                result = cursor.fetchall()
                cursor.close()
                return result
        except Exception as e:
            self.logger.error(f"Error en consulta: {str(e)}")
            raise
    
    async def execute_update(self, query: str, params: tuple | None = None) -> int:
        """Ejecutar consulta INSERT/UPDATE/DELETE"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                return affected_rows
        except Exception as e:
            self.logger.error(f"Error en actualización: {str(e)}")
            raise
    
    async def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """Ejecutar consultas en lote"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                return affected_rows
        except Exception as e:
            self.logger.error(f"Error en lote: {str(e)}")
            raise
    
    # Métodos específicos para películas (usando tablas XUI One)
    async def get_movies(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener películas con paginación desde streams (type=2)"""
        return await self.execute_query(QUERIES["get_movies"], (limit, offset))

    async def search_movies(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar películas por título"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_movies"], (term, term, limit))

    async def get_movie_by_id(self, movie_id: int) -> Optional[Dict]:
        """Obtener película por ID"""
        result = await self.execute_query(QUERIES["get_movie_by_id"], (movie_id,))
        return result[0] if result else None

    async def update_movie_tmdb(self, stream_id: int, tmdb_data: Dict) -> int:
        """Actualizar información TMDB de una película"""
        movie_properties = json.dumps(tmdb_data.get('movie_properties', {}))
        return await self.execute_update(
            QUERIES["update_stream_tmdb"],
            (tmdb_data.get('tmdb_id'), movie_properties, tmdb_data.get('year'),
             tmdb_data.get('rating'), stream_id)
        )
    
    # Métodos específicos para series de TV (usando tablas XUI One)
    async def get_tv_shows(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener series de TV con paginación desde streams_series"""
        return await self.execute_query(QUERIES["get_series"], (limit, offset))

    async def search_tv_shows(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar series por nombre"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_series"], (term, term, limit))

    async def get_tv_show_by_id(self, series_id: int) -> Optional[Dict]:
        """Obtener serie por ID"""
        result = await self.execute_query(QUERIES["get_series_by_id"], (series_id,))
        return result[0] if result else None

    async def get_series_episodes(self, series_id: int) -> List[Dict]:
        """Obtener episodios de una serie"""
        return await self.execute_query(QUERIES["get_series_episodes"], (series_id,))

    async def update_series_tmdb(self, series_id: int, tmdb_data: Dict) -> int:
        """Actualizar información TMDB de una serie"""
        return await self.execute_update(
            QUERIES["update_series_tmdb"],
            (tmdb_data.get('tmdb_id'), tmdb_data.get('plot'), tmdb_data.get('cast'),
             tmdb_data.get('rating'), tmdb_data.get('backdrop_path'),
             tmdb_data.get('youtube_trailer'), tmdb_data.get('year'), series_id)
        )
    
    # Métodos para categorías y tipos de streams
    async def get_categories(self) -> List[Dict]:
        """Obtener todas las categorías"""
        return await self.execute_query(QUERIES["get_categories"])

    async def get_categories_by_type(self, category_type: str) -> List[Dict]:
        """Obtener categorías por tipo"""
        return await self.execute_query(QUERIES["get_categories_by_type"], (category_type,))

    async def get_stream_types(self) -> List[Dict]:
        """Obtener tipos de streams"""
        return await self.execute_query(QUERIES["get_stream_types"])

    # Métodos para streams en general
    async def get_streams(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener todos los streams con paginación"""
        return await self.execute_query(QUERIES["get_streams"], (limit, offset))

    async def get_stream_by_id(self, stream_id: int) -> Optional[Dict]:
        """Obtener stream por ID"""
        result = await self.execute_query(QUERIES["get_stream_by_id"], (stream_id,))
        return result[0] if result else None

    async def search_streams(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar streams por nombre"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_streams"], (term, term, limit))

    async def get_live_streams(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener streams de TV en vivo (type=1)"""
        return await self.execute_query(QUERIES["get_live_streams"], (limit, offset))
    
    # Métodos para análisis y estadísticas
    async def get_content_stats(self) -> Dict:
        """Obtener estadísticas de contenido"""
        result = await self.execute_query(QUERIES["get_content_stats"])
        return result[0] if result else {}

    async def get_duplicate_streams(self) -> List[Dict]:
        """Obtener streams duplicados"""
        return await self.execute_query(QUERIES["get_duplicate_streams"])

    async def get_streams_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener streams sin información TMDB"""
        return await self.execute_query(QUERIES["get_streams_without_tmdb"], (limit,))

    async def get_movies_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener películas sin información TMDB"""
        return await self.execute_query(QUERIES["get_movies_without_tmdb"], (limit,))

    async def get_series_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener series sin información TMDB"""
        return await self.execute_query(QUERIES["get_series_without_tmdb"], (limit,))

    async def get_popular_movies(self, limit: int = 50) -> List[Dict]:
        """Obtener películas populares"""
        return await self.execute_query(QUERIES["get_popular_movies"], (limit,))

    async def get_recent_movies(self, days: int = 30, limit: int = 50) -> List[Dict]:
        """Obtener películas recientes"""
        return await self.execute_query(QUERIES["get_recent_movies"], (days, limit))

    async def get_movies_by_year(self, year: int, limit: int = 50) -> List[Dict]:
        """Obtener películas por año"""
        return await self.execute_query(QUERIES["get_movies_by_year"], (year, limit))

    async def get_broken_streams(self, limit: int = 100) -> List[Dict]:
        """Obtener streams con problemas"""
        return await self.execute_query(QUERIES["get_broken_streams"], (limit,))
    
    # Métodos para eliminación de contenido
    async def delete_stream(self, stream_id: int) -> int:
        """Eliminar stream"""
        return await self.execute_update(QUERIES["delete_stream"], (stream_id,))

    async def delete_series(self, series_id: int) -> int:
        """Eliminar serie"""
        return await self.execute_update(QUERIES["delete_series"], (series_id,))

    # Métodos de sesión simplificados (para compatibilidad con la UI)
    async def get_session(self, session_id: str) -> Optional[Dict]:
        """Obtener sesión (método simplificado para XUI One)"""
        # Para XUI One, simplemente retornamos None ya que no manejamos sesiones complejas
        return None

    async def save_session(self, session_id: str, user_data: Dict, preferences: Dict):
        """Guardar sesión (método simplificado para XUI One)"""
        # Para XUI One, simplemente loggeamos que se intentó guardar la sesión
        self.logger.info(f"Sesión {session_id} guardada (modo simplificado)")
        pass

    # Métodos de cache simplificados (para compatibilidad con TMDB)
    async def get_cache(self, key: str) -> Optional[str]:
        """Obtener valor del cache (método simplificado para XUI One)"""
        # Para XUI One, no implementamos cache persistente, siempre retornamos None
        return None

    async def set_cache(self, key: str, value: str, ttl: int = 3600):
        """Establecer valor en cache (método simplificado para XUI One)"""
        # Para XUI One, simplemente loggeamos que se intentó guardar en cache
        self.logger.debug(f"Cache {key} guardado (modo simplificado)")
        pass
    
    async def close(self):
        """Cerrar conexiones"""
        if self.pool:
            # Cerrar todas las conexiones del pool
            self.logger.info("Cerrando pool de conexiones")
            self._initialized = False
